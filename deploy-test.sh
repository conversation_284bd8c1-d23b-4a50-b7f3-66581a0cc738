#!/bin/bash

# Set the kubeconfig for K3s
export KUBECONFIG=/etc/rancher/k3s/k3s.yaml

echo "🚀 Deploying Hello World Helm Chart to test namespace..."

# Create test namespace
echo "Creating test namespace..."
kubectl create namespace test --dry-run=client -o yaml | kubectl apply -f -

# Deploy the Helm chart
echo "Deploying Helm chart..."
/usr/local/bin/helm install hello-world ./hello-world-chart \
  --namespace test \
  --set ingress.enabled=false \
  --wait --timeout=300s

# Check deployment status
echo "Checking deployment status..."
kubectl get pods -n test -l app.kubernetes.io/instance=hello-world

echo "Checking services..."
kubectl get svc -n test

# Wait for pods to be ready
echo "Waiting for pods to be ready..."
kubectl wait --for=condition=ready pod -l app.kubernetes.io/instance=hello-world -n test --timeout=120s

echo "✅ Deployment complete!"
echo ""
echo "🔗 To access the applications, run these commands in separate terminals:"
echo ""
echo "Frontend (webapp):"
echo "  kubectl port-forward -n test svc/hello-world-webapp 8080:80"
echo "  Then visit: http://localhost:8080"
echo ""
echo "Backend (API):"
echo "  kubectl port-forward -n test svc/hello-world-api 8081:8080"
echo "  Then visit: http://localhost:8081"
echo ""
echo "🧪 Quick tests:"
echo "  curl http://localhost:8080        # Frontend"
echo "  curl http://localhost:8081/health # Backend health"
echo ""
echo "📊 Monitor with:"
echo "  kubectl get pods -n test -w"
echo "  kubectl logs -n test -l app.kubernetes.io/component=webapp -f"
echo ""
echo "🗑️  To cleanup:"
echo "  helm uninstall hello-world -n test"
echo "  kubectl delete namespace test"
