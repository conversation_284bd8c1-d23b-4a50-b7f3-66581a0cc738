#!/bin/bash

# Set the kubeconfig for K3s
export KUBECONFIG=/etc/rancher/k3s/k3s.yaml

echo "🔗 Setting up port-forwarding for Hello World application..."

# Check if pods are running
echo "Checking pod status..."
kubectl get pods -n test -l app.kubernetes.io/instance=hello-world

# Function to cleanup background processes
cleanup() {
    echo "🛑 Stopping port-forwarding..."
    jobs -p | xargs -r kill
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM EXIT

echo ""
echo "🌐 Starting port-forwarding..."
echo "Frontend will be available at: http://localhost:8080"
echo "Backend API will be available at: http://localhost:8081"
echo ""
echo "Press Ctrl+C to stop port-forwarding"
echo ""

# Start port-forwarding in background
kubectl port-forward -n test svc/hello-world-webapp 8080:80 &
WEBAPP_PID=$!

kubectl port-forward -n test svc/hello-world-api 8081:8080 &
API_PID=$!

# Wait a moment for port-forwards to establish
sleep 3

echo "✅ Port-forwarding active!"
echo ""
echo "🧪 Test commands:"
echo "  curl http://localhost:8080        # Frontend webapp"
echo "  curl http://localhost:8081/health # Backend health check"
echo "  curl http://localhost:8081/       # Backend info page"
echo ""
echo "🌐 Open in browser:"
echo "  Frontend: http://localhost:8080"
echo "  Backend:  http://localhost:8081"
echo ""

# Wait for background processes
wait $WEBAPP_PID $API_PID
