# Hello World Kubernetes Helm Chart

A simple Hello World application for testing Kubernetes setups. This chart deploys a frontend webapp and a backend API service to validate your K8s cluster functionality.

## Features

- 🌐 **Frontend Webapp**: Interactive HTML page with backend connectivity test
- 🔧 **Backend API**: Simple REST API with health check endpoint
- 📊 **Monitoring**: Built-in health checks and readiness probes
- 🔄 **Scaling**: Horizontal Pod Autoscaler support
- 🌍 **Ingress**: Configurable ingress for external access
- 🛡️ **Security**: Service accounts and security contexts

## Quick Start

### Prerequisites

- Kubernetes cluster (1.19+)
- Helm 3.x
- kubectl configured to access your cluster

### Installation

1. **Install the chart:**
   ```bash
   helm install hello-world ./hello-world-chart
   ```

2. **Install with custom values:**
   ```bash
   helm install hello-world ./hello-world-chart \
     --set ingress.hosts[0].host=my-app.local \
     --set replicaCount=2
   ```

3. **Install in specific namespace:**
   ```bash
   kubectl create namespace test-app
   helm install hello-world ./hello-world-chart -n test-app
   ```

### Testing the Deployment

1. **Check pod status:**
   ```bash
   kubectl get pods -l app.kubernetes.io/instance=hello-world
   ```

2. **Test frontend (port-forward):**
   ```bash
   kubectl port-forward svc/hello-world-webapp 8080:80
   # Visit http://localhost:8080
   ```

3. **Test backend API:**
   ```bash
   kubectl port-forward svc/hello-world-api 8081:8080
   curl http://localhost:8081/health
   ```

4. **Test with ingress (if enabled):**
   ```bash
   # Add to /etc/hosts: <ingress-ip> hello-world.local
   curl http://hello-world.local/
   curl http://hello-world.local/api/health
   ```

## Configuration

### Key Values

| Parameter | Description | Default |
|-----------|-------------|---------|
| `replicaCount` | Number of replicas | `1` |
| `frontend.image.repository` | Frontend image | `nginx` |
| `frontend.image.tag` | Frontend image tag | `1.25-alpine` |
| `backend.image.repository` | Backend image | `httpd` |
| `backend.image.tag` | Backend image tag | `2.4-alpine` |
| `ingress.enabled` | Enable ingress | `true` |
| `ingress.hosts[0].host` | Ingress hostname | `hello-world.local` |
| `autoscaling.enabled` | Enable HPA | `false` |

### Example Custom Values

```yaml
# values-custom.yaml
replicaCount: 3

frontend:
  resources:
    requests:
      cpu: 100m
      memory: 128Mi

backend:
  resources:
    requests:
      cpu: 100m
      memory: 128Mi

ingress:
  enabled: true
  hosts:
    - host: my-test-app.example.com
      paths:
        - path: /
          pathType: Prefix
          service: webapp
        - path: /api
          pathType: Prefix
          service: api

autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
```

## Troubleshooting

### Common Issues

1. **Pods not starting:**
   ```bash
   kubectl describe pods -l app.kubernetes.io/instance=hello-world
   kubectl logs -l app.kubernetes.io/component=webapp
   ```

2. **Ingress not working:**
   ```bash
   kubectl get ingress
   kubectl describe ingress hello-world
   ```

3. **Backend connectivity issues:**
   ```bash
   kubectl exec -it deployment/hello-world-webapp -- wget -qO- http://hello-world-api:8080/health
   ```

### Useful Commands

```bash
# View all resources
kubectl get all -l app.kubernetes.io/instance=hello-world

# Check resource usage
kubectl top pods -l app.kubernetes.io/instance=hello-world

# Scale manually
kubectl scale deployment hello-world-webapp --replicas=5

# Update configuration
helm upgrade hello-world ./hello-world-chart --set replicaCount=3

# Uninstall
helm uninstall hello-world
```

## Architecture

```
┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │
│   (nginx)       │    │    (httpd)      │
│   Port: 80      │    │    Port: 80     │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────────────────┘
                     │
         ┌─────────────────┐
         │    Ingress      │
         │  hello-world    │
         │   /     /api    │
         └─────────────────┘
```

## License

This is a test/demo chart for educational purposes.
