# Default values for hello-world chart
replicaCount: 1

# Frontend webapp configuration
frontend:
  name: webapp
  image:
    repository: nginx
    tag: "1.25-alpine"
    pullPolicy: IfNotPresent
  
  service:
    type: ClusterIP
    port: 80
    targetPort: 80
  
  resources:
    limits:
      cpu: 100m
      memory: 128Mi
    requests:
      cpu: 50m
      memory: 64Mi

# Backend service configuration  
backend:
  name: api
  image:
    repository: httpd
    tag: "2.4-alpine"
    pullPolicy: IfNotPresent
  
  service:
    type: ClusterIP
    port: 8080
    targetPort: 80
  
  resources:
    limits:
      cpu: 100m
      memory: 128Mi
    requests:
      cpu: 50m
      memory: 64Mi

# Ingress configuration
ingress:
  enabled: true
  className: ""
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: hello-world.local
      paths:
        - path: /
          pathType: Prefix
          service: webapp
        - path: /api
          pathType: Prefix
          service: api
  tls: []
  #  - secretName: hello-world-tls
  #    hosts:
  #      - hello-world.local

# Service account
serviceAccount:
  create: true
  annotations: {}
  name: ""

# Pod security context
podSecurityContext: {}
  # fsGroup: 2000

# Container security context
securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

# Node selector
nodeSelector: {}

# Tolerations
tolerations: []

# Affinity
affinity: {}

# Autoscaling
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80
